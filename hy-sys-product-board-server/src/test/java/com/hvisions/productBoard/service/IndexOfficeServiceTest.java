package com.hvisions.productBoard.service;

import com.hvisions.productBoard.resp.IndexOfficeDataOverviewResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class IndexOfficeServiceTest {

    @Resource
    private IndexOfficeService indexOfficeService;

    @Test
    public void testGetDataOverview() {
        try {
            IndexOfficeDataOverviewResp resp = indexOfficeService.getDataOverview();
            
            log.info("数据概览测试结果:");
            log.info("新增合同数: {}", resp.getNewOrderCount());
            log.info("产品入库数: {}", resp.getProductQuantity());
            log.info("瓶颈订单数: {}", resp.getBottleneckOrderCount());
            log.info("异常订单数: {}", resp.getErrorOrderCount());
            log.info("临期订单数: {}", resp.getNearOrderCount());
            
            // 基本验证
            assert resp != null;
            assert resp.getNewOrderCount() != null;
            assert resp.getProductQuantity() != null;
            assert resp.getBottleneckOrderCount() != null;
            assert resp.getErrorOrderCount() != null;
            assert resp.getNearOrderCount() != null;
            
            // 瓶颈订单数应该为0（暂不实现）
            assert resp.getBottleneckOrderCount().equals(0);
            
            log.info("IndexOfficeService.getDataOverview() 测试通过");
            
        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }
}
