package com.hvisions.productBoard.service;

import com.hvisions.productBoard.resp.IndexOfficeDataOverviewResp;
import com.hvisions.productBoard.resp.IndexOfficeUrgentOrderResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class IndexOfficeServiceTest {

    @Resource
    private IndexOfficeService indexOfficeService;

    @Test
    public void testGetDataOverview() {
        IndexOfficeDataOverviewResp resp = indexOfficeService.getDataOverview();
        
        log.info("数据概览测试结果:");
        log.info("新增合同数: {}", resp.getNewOrderCount());
        log.info("产品入库数: {}", resp.getProductQuantity());
        log.info("瓶颈订单数: {}", resp.getBottleneckOrderCount());
        log.info("异常订单数: {}", resp.getErrorOrderCount());
        log.info("临期订单数: {}", resp.getNearOrderCount());
        
        // 基本验证
        assert resp != null;
        assert resp.getNewOrderCount() != null;
        assert resp.getProductQuantity() != null;
        assert resp.getBottleneckOrderCount() != null;
        assert resp.getErrorOrderCount() != null;
        assert resp.getNearOrderCount() != null;
        
        // 瓶颈订单数应该为0（暂不实现）
        assert resp.getBottleneckOrderCount().equals(0);
        
        log.info("IndexOfficeService.getDataOverview() 测试通过");
    }

    @Test
    public void testGetUrgentOrderSales() {
        Page<IndexOfficeUrgentOrderResp> resp = indexOfficeService.getUrgentOrder(1, 10, "销售", null);
        
        log.info("销售临期订单测试结果:");
        log.info("总记录数: {}", resp.getTotalElements());
        log.info("当前页: {}", resp.getNumber());
        log.info("每页大小: {}", resp.getSize());
        log.info("记录数: {}", resp.getContent().size());

        resp.getContent().forEach(order -> {
            log.info("合同号: {}, 客户: {}, 交付日期: {}, 进度: {}", 
                    order.getContractCode(), order.getCustomerName(), 
                    order.getDeliveryDate(), order.getProgress());
        });
        
        assert resp != null;
        assert resp.getContent() != null;
        
        log.info("IndexOfficeService.getUrgentOrder(销售) 测试通过");
    }

    @Test
    public void testGetUrgentOrderWithContractCode() {
        Page<IndexOfficeUrgentOrderResp> resp = indexOfficeService.getUrgentOrder(1, 10, "销售", "TEST");
        
        log.info("销售订单（指定合同号）测试结果:");
        log.info("总记录数: {}", resp.getTotalElements());
        log.info("记录数: {}", resp.getContent().size());

        resp.getContent().forEach(order -> {
            log.info("合同号: {}, 客户: {}, 交付日期: {}, 进度: {}",
                    order.getContractCode(), order.getCustomerName(),
                    order.getDeliveryDate(), order.getProgress());
        });

        assert resp != null;
        assert resp.getContent() != null;
        
        log.info("IndexOfficeService.getUrgentOrder(销售-指定合同号) 测试通过");
    }

    @Test
    public void testGetUrgentOrderResearch() {
        Page<IndexOfficeUrgentOrderResp> resp = indexOfficeService.getUrgentOrder(1, 10, "科研", null);
        
        log.info("科研订单测试结果:");
        log.info("总记录数: {}", resp.getTotalElements());
        log.info("记录数: {}", resp.getContent().size());

        // 科研订单暂不实现，应该返回空页面
        assert resp != null;
        assert resp.getTotalElements() == 0;
        assert resp.getContent().isEmpty();
        
        log.info("IndexOfficeService.getUrgentOrder(科研) 测试通过");
    }

    @Test
    public void testGetUrgentOrderTest() {
        Page<IndexOfficeUrgentOrderResp> resp = indexOfficeService.getUrgentOrder(1, 10, "试验", null);
        
        log.info("试验订单测试结果:");
        log.info("总记录数: {}", resp.getTotalElements());
        log.info("记录数: {}", resp.getContent().size());

        // 试验订单暂不实现，应该返回空页面
        assert resp != null;
        assert resp.getTotalElements() == 0;
        assert resp.getContent().isEmpty();
        
        log.info("IndexOfficeService.getUrgentOrder(试验) 测试通过");
    }
}
