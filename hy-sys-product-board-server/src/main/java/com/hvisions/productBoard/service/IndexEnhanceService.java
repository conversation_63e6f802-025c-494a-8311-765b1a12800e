package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.productBoard.entity.HyPmEnhanceWorkOrder;
import com.hvisions.productBoard.enums.EnhanceWorkOrderStateEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.mapper.HyPmEnhanceWorkOrderMapper;
import com.hvisions.productBoard.req.CommonPageReq;
import com.hvisions.productBoard.resp.IndexEnhanceDataOverviewResp;
import com.hvisions.productBoard.resp.IndexEnhanceKeyOrderResp;
import com.hvisions.productBoard.resp.PmEnhanceCompleteRateResp;
import com.hvisions.productBoard.util.PageHelperAdapter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndexEnhanceService {

    @Resource
    private PmEnhanceService pmEnhanceService;

    @Resource
    private HyPmEnhanceWorkOrderMapper hyPmEnhanceWorkOrderMapper;

    public IndexEnhanceDataOverviewResp getDataOverview() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(LocalTime.MAX);

        List<HyPmEnhanceWorkOrder> ongoingOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .eq(HyPmEnhanceWorkOrder::getState, EnhanceWorkOrderStateEnum.PRODUCING.getValue())
        );
        Integer ongoingOrder = ongoingOrders.size();

        List<HyPmEnhanceWorkOrder> newOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .between(HyPmEnhanceWorkOrder::getCreateTime, todayStart, todayEnd)
        );
        Integer newOrder = newOrders.size();

        LocalDateTime nearDate = today.plusMonths(1).atTime(LocalTime.MAX);
        List<HyPmEnhanceWorkOrder> nearOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .between(HyPmEnhanceWorkOrder::getPlanEndTime, todayStart, nearDate)
        );
        Integer nearOrder = nearOrders.size();

        List<HyPmEnhanceWorkOrder> todayFinishedOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .eq(HyPmEnhanceWorkOrder::getState, EnhanceWorkOrderStateEnum.PRODUCED.getValue())
                        .between(HyPmEnhanceWorkOrder::getActualEndTime, todayStart, todayEnd)
        );
        Integer todayFinishOrder = todayFinishedOrders.size();

        BigDecimal todayOutput = todayFinishedOrders.stream()
                .map(order -> order.getFinishedQuantity() != null ? order.getFinishedQuantity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return IndexEnhanceDataOverviewResp.builder()
                .ongoingOrder(ongoingOrder)
                .newOrder(newOrder)
                .nearOrder(nearOrder)
                .todayFinishOrder(todayFinishOrder)
                .todayOutput(todayOutput)
                .build();
    }

    public List<PmEnhanceCompleteRateResp> getCompleteRate(TimePeriodEnum periodEnum) {
        return pmEnhanceService.getCompleteRate(periodEnum);
    }

    public Page<IndexEnhanceKeyOrderResp> getKeyOrder(Integer current, Integer size, String orderCode, String productName, String productEigenvalue, String batchNum) {
        CommonPageReq pageReq = PageHelperAdapter.createPageReq(current, size);

        org.springframework.data.domain.Page<HyPmEnhanceWorkOrder> page = PageHelperUtil.getPage(
                req -> hyPmEnhanceWorkOrderMapper.selectListForKeyOrder(req, orderCode, productName, productEigenvalue, batchNum), pageReq);

        List<IndexEnhanceKeyOrderResp> records = page.getContent().stream()
                .map(order -> {
                    double percentage = 0.0;
                    if (order.getPlanQuantity() != null && order.getPlanQuantity().doubleValue() > 0) {
                        double finishedQty = order.getFinishedQuantity() != null ? order.getFinishedQuantity().doubleValue() : 0.0;
                        percentage = (finishedQty / order.getPlanQuantity().doubleValue()) * 100;
                    }

                    return IndexEnhanceKeyOrderResp.builder()
                            .orderCode(order.getCode())
                            .productName(order.getMaterialName())
                            .productEigenvalue(order.getMaterialEigenvalue())
                            .orderQuantity(order.getPlanQuantity() != null ? order.getPlanQuantity().intValue() : 0)
                            .batchNum(order.getMaterialBatchNum())
                            .percentage(percentage)
                            .productionQuantity(order.getFinishedQuantity() != null ? order.getFinishedQuantity() : BigDecimal.ZERO)
                            .build();
                })
                .collect(Collectors.toList());

        return new PageImpl<>(records, PageRequest.of(current - 1, size), page.getTotalElements());
    }

    public String getKeyMaterials() {
        return null;
    }

    public String getNotice() {
        return null;
    }
}
