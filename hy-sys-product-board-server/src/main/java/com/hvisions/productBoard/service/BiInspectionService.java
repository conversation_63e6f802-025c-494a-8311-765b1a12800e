package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.productBoard.entity.HyQmFinishedNonconformityReview;
import com.hvisions.productBoard.entity.HyQmHoseAssemblyOrder;
import com.hvisions.productBoard.entity.HyQmVulcanizeQualityOrder;
import com.hvisions.productBoard.enums.HoseInspectionStateEnum;
import com.hvisions.productBoard.enums.VulcanizeOrderStateEnum;
import com.hvisions.productBoard.mapper.HyQmFinishedNonconformityReviewMapper;
import com.hvisions.productBoard.mapper.HyQmHoseAssemblyOrderMapper;
import com.hvisions.productBoard.mapper.HyQmVulcanizeQualityOrderMapper;
import com.hvisions.productBoard.req.CommonPageReq;
import com.hvisions.productBoard.resp.*;
import com.hvisions.productBoard.util.PageHelperAdapter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiInspectionService {

    @Resource
    private HyQmHoseAssemblyOrderMapper hyQmHoseAssemblyOrderMapper;

    @Resource
    private HyQmFinishedNonconformityReviewMapper hyQmFinishedNonconformityReviewMapper;

    @Resource
    private HyQmVulcanizeQualityOrderMapper hyQmVulcanizeQualityOrderMapper;

    public BiInspectionDataOverviewResp getDataOverview() {
        // 待检任务：软管组件检验单，状态为待质检的任务数量
        List<HyQmHoseAssemblyOrder> pendingOrders = hyQmHoseAssemblyOrderMapper.selectList(
                Wrappers.lambdaQuery(HyQmHoseAssemblyOrder.class)
                        .eq(HyQmHoseAssemblyOrder::getQualityState, HoseInspectionStateEnum.PENDING_QA.getValue())
        );

        int taskCount = pendingOrders.size();
        int taskQuantity = pendingOrders.stream()
                .mapToInt(order -> (order.getTotalCount() != null ? order.getTotalCount() : 0) -
                        (order.getQualityPassCount() != null ? order.getQualityPassCount() : 0))
                .sum();

        // 本月新增任务：本月新增的软管组件检验单（包括未完成）
        LocalDateTime startOfMonth = LocalDate.now().withDayOfMonth(1).atStartOfDay();
        LocalDateTime endOfMonth = LocalDate.now().plusMonths(1).withDayOfMonth(1).atStartOfDay();

        List<HyQmHoseAssemblyOrder> newOrders = hyQmHoseAssemblyOrderMapper.selectList(
                Wrappers.lambdaQuery(HyQmHoseAssemblyOrder.class)
                        .between(HyQmHoseAssemblyOrder::getCreateTime, startOfMonth, endOfMonth)
        );

        int newTaskCount = newOrders.size();
        int newTaskQuantity = newOrders.stream()
                .mapToInt(order -> order.getTotalCount() != null ? order.getTotalCount() : 0)
                .sum();

        return BiInspectionDataOverviewResp.builder()
                .taskCount(taskCount)
                .taskQuantity(taskQuantity)
                .newTaskCount(newTaskCount)
                .newTaskQuantity(newTaskQuantity)
                .build();
    }

    public List<BiInspectionUnqualifiedDistributionResp> getUnqualifiedDistribution() {

        LocalDateTime threeMonthsAgo = LocalDate.now().minusMonths(3).atStartOfDay();
        LocalDateTime now = LocalDateTime.now();

        List<HyQmFinishedNonconformityReview> reviews = hyQmFinishedNonconformityReviewMapper.selectList(
                Wrappers.lambdaQuery(HyQmFinishedNonconformityReview.class)
                        .between(HyQmFinishedNonconformityReview::getCreateTime, threeMonthsAgo, now)
                        .isNotNull(HyQmFinishedNonconformityReview::getRemark)
        );

        Map<String, Long> typeCountMap = reviews.stream()
                .collect(Collectors.groupingBy(HyQmFinishedNonconformityReview::getRemark, Collectors.counting()));

        long totalCount = reviews.size();

        return typeCountMap.entrySet().stream()
                .map(entry -> BiInspectionUnqualifiedDistributionResp.builder()
                        .type(entry.getKey())
                        .quantity(entry.getValue().intValue())
                        .percentage((double) entry.getValue() / totalCount * 100)
                        .build())
                .collect(Collectors.toList());
    }

    public Page<BiInspectionVulcanizeOrderResp> getVulcanizeOrder(Integer current, Integer size) {
        CommonPageReq pageReq = PageHelperAdapter.createPageReq(current, size);

        org.springframework.data.domain.Page<HyQmVulcanizeQualityOrder> page = PageHelperUtil.getPage(
                hyQmVulcanizeQualityOrderMapper::selectListForVulcanizeOrder, pageReq);

        List<BiInspectionVulcanizeOrderResp> list = page.getContent().stream()
                .map(order -> BiInspectionVulcanizeOrderResp.builder()
                        .code(order.getWorkOrderCode())
                        .quantity(order.getTotalCount())
                        .state(VulcanizeOrderStateEnum.NEW.getValue().equals(order.getVulcanizeState())
                            ? VulcanizeOrderStateEnum.NEW.getName()
                            : VulcanizeOrderStateEnum.RETURN_FACTORY.getName())
                        .build())
                .collect(Collectors.toList());

        return new PageImpl<>(list, PageRequest.of(current - 1, size), page.getTotalElements());
    }

    public List<BiInspectionCompleteOutputResp> getCompleteOutput() {

        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(6);

        List<BiInspectionCompleteOutputResp> result = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            LocalDateTime dayStart = date.atStartOfDay();
            LocalDateTime dayEnd = date.plusDays(1).atStartOfDay();

            List<HyQmHoseAssemblyOrder> orders = hyQmHoseAssemblyOrderMapper.selectList(
                    Wrappers.lambdaQuery(HyQmHoseAssemblyOrder.class)
                            .in(HyQmHoseAssemblyOrder::getQualityState,
                                    HoseInspectionStateEnum.PENDING_TRAIL.getValue(),
                                    HoseInspectionStateEnum.PASSED.getValue())
                            .between(HyQmHoseAssemblyOrder::getQualityTime, dayStart, dayEnd)
            );

            int orderCount = orders.size();
            int productCount = orders.stream()
                    .mapToInt(order -> order.getQualityPassCount() != null ? order.getQualityPassCount() : 0)
                    .sum();

            result.add(BiInspectionCompleteOutputResp.builder()
                    .orderCount(orderCount)
                    .productCount(productCount)
                    .date(date.format(formatter))
                    .build());
        }

        return result;
    }

    public List<BiInspectionKeyOrderResp> getKeyOrder() {
        return null;
    }

    public String getNotice() {
        return null;
    }
}
