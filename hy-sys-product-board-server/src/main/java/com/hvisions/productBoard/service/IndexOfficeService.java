package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.productBoard.entity.HyWmsCompleteDeliverOrder;
import com.hvisions.productBoard.entity.SaSaleOrder;
import com.hvisions.productBoard.enums.DepartmentEnum;
import com.hvisions.productBoard.enums.InspectionTypeEnum;
import com.hvisions.productBoard.enums.PackageIndexTypeEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.mapper.HyWmsCompleteDeliverOrderMapper;
import com.hvisions.productBoard.mapper.SaSaleOrderMapper;
import com.hvisions.productBoard.req.CommonPageReq;
import com.hvisions.productBoard.resp.CompleteResp;
import com.hvisions.productBoard.resp.IndexOfficeDataOverviewResp;
import com.hvisions.productBoard.resp.IndexOfficeUrgentOrderResp;
import com.hvisions.productBoard.util.PageHelperAdapter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndexOfficeService {

    @Resource
    private PmEnhanceService pmEnhanceService;

    @Resource
    private PmMoldingService pmMoldingService;

    @Resource
    private PmInspectionService pmInspectionService;

    @Resource
    private PmPackageService pmPackageService;

    @Resource
    private SaSaleOrderMapper saSaleOrderMapper;

    @Resource
    private HyWmsCompleteDeliverOrderMapper hyWmsCompleteDeliverOrderMapper;

    public IndexOfficeDataOverviewResp getDataOverview() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(LocalTime.MAX);

        Long newOrderCountLong = saSaleOrderMapper.selectCount(
                new QueryWrapper<SaSaleOrder>().lambda()
                        .between(SaSaleOrder::getCreateTime, todayStart, todayEnd)
        );
        Integer newOrderCount = newOrderCountLong != null ? newOrderCountLong.intValue() : 0;

        List<HyWmsCompleteDeliverOrder> deliverOrders = hyWmsCompleteDeliverOrderMapper.selectList(
                new QueryWrapper<HyWmsCompleteDeliverOrder>().lambda()
                        .between(HyWmsCompleteDeliverOrder::getFinishDate, todayStart, todayEnd)
                        .isNotNull(HyWmsCompleteDeliverOrder::getActualQuantity)
        );

        BigDecimal totalQuantity = deliverOrders.stream()
                .map(order -> order.getActualQuantity() != null ? order.getActualQuantity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer productQuantity = totalQuantity.intValue();

        // TODO 瓶颈订单数
        Integer bottleneckOrderCount = 0;

        Long errorOrderCountLong = saSaleOrderMapper.selectCount(
                new QueryWrapper<SaSaleOrder>().lambda()
                        .lt(SaSaleOrder::getPlanFinishDate, today)
                        .notIn(SaSaleOrder::getShippingState, 20, 40)
        );
        Integer errorOrderCount = errorOrderCountLong != null ? errorOrderCountLong.intValue() : 0;

        LocalDate oneMonthLater = today.plusMonths(1);
        Long nearOrderCountLong = saSaleOrderMapper.selectCount(
                new QueryWrapper<SaSaleOrder>().lambda()
                        .ge(SaSaleOrder::getPlanFinishDate, today)
                        .le(SaSaleOrder::getPlanFinishDate, oneMonthLater)
                        .notIn(SaSaleOrder::getShippingState, 20, 40)
        );
        Integer nearOrderCount = nearOrderCountLong != null ? nearOrderCountLong.intValue() : 0;

        return IndexOfficeDataOverviewResp.builder()
                .newOrderCount(newOrderCount)
                .productQuantity(productQuantity)
                .bottleneckOrderCount(bottleneckOrderCount)
                .errorOrderCount(errorOrderCount)
                .nearOrderCount(nearOrderCount)
                .build();
    }

    public List<CompleteResp> getCompleteRateYear(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.YEAR);
    }

    public List<CompleteResp> getCompleteRateMonth(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.MONTH);
    }

    public List<CompleteResp> getCompleteRateWeek(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.WEEK);
    }

    @SuppressWarnings("unchecked")
    private List<CompleteResp> getCompleteRateByDepartmentAndPeriod(DepartmentEnum department, TimePeriodEnum period) {
        switch (department) {
            case ENHANCE:
                return (List<CompleteResp>) (List<?>) pmEnhanceService.getCompleteRate(period);
            case MOLDING:
                return (List<CompleteResp>) (List<?>) pmMoldingService.getCompleteRate(period);
            case INSPECTION:
                return (List<CompleteResp>) (List<?>) pmInspectionService.getOutput(period, InspectionTypeEnum.QUALITY);
            case PACKAGE:
                return (List<CompleteResp>) (List<?>) pmPackageService.getOutput(period, PackageIndexTypeEnum.PACKAGE);
            default:
                throw new IllegalArgumentException("不支持的部门类型: " + department);
        }
    }

    public Page<IndexOfficeUrgentOrderResp> getUrgentOrder(Integer current, Integer size, String type, String contractCode) {
        if ("销售".equals(type)) {
            return getSalesUrgentOrder(current, size, contractCode);
        } else if ("科研".equals(type) || "试验".equals(type)) {
            return null;
        } else {
            throw new IllegalArgumentException("不支持的订单类型: " + type);
        }
    }

    private Page<IndexOfficeUrgentOrderResp> getSalesUrgentOrder(Integer current, Integer size, String contractCode) {
        CommonPageReq pageReq = PageHelperAdapter.createPageReq(current, size);

        return PageHelperUtil.getPage(
                req -> getSalesOrderList(contractCode).stream()
                        .map(this::convertToUrgentOrderResp)
                        .collect(Collectors.toList()),
                pageReq);
    }

    private List<SaSaleOrder> getSalesOrderList(String contractCode) {
        LocalDate today = LocalDate.now();
        LocalDate oneMonthLater = today.plusMonths(1);

        LambdaQueryWrapper<SaSaleOrder> queryWrapper = new QueryWrapper<SaSaleOrder>().lambda()
                .notIn(SaSaleOrder::getShippingState, 20, 40);

        if (StringUtils.hasText(contractCode)) {
            queryWrapper.like(SaSaleOrder::getContractCode, contractCode);
        } else {
            queryWrapper.ge(SaSaleOrder::getPlanFinishDate, today)
                    .le(SaSaleOrder::getPlanFinishDate, oneMonthLater);
        }

        queryWrapper.orderByAsc(SaSaleOrder::getPlanFinishDate);

        return saSaleOrderMapper.selectList(queryWrapper);
    }

    private IndexOfficeUrgentOrderResp convertToUrgentOrderResp(SaSaleOrder saleOrder) {

        BigDecimal progress = BigDecimal.ZERO;
        if (saleOrder.getPlanCount() != null && saleOrder.getPlanCount().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal completedCount = saleOrder.getWareCount() != null ? saleOrder.getWareCount() : BigDecimal.ZERO;
            progress = completedCount.divide(saleOrder.getPlanCount(), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }

        String progressStr = progress.setScale(2, RoundingMode.HALF_UP) + "%";

        return IndexOfficeUrgentOrderResp.builder()
                .contractCode(saleOrder.getContractCode())
                .customerName(saleOrder.getCustomerName())
                .deliveryDate(saleOrder.getPlanFinishDate() != null ?
                        saleOrder.getPlanFinishDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : "")
                .progress(progressStr)
                .build();
    }

    public String getUrgentOrderDetail() {
        return null;
    }
}
