package com.hvisions.productBoard.service;

import com.hvisions.productBoard.enums.DepartmentEnum;
import com.hvisions.productBoard.enums.InspectionTypeEnum;
import com.hvisions.productBoard.enums.PackageIndexTypeEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.resp.CompleteResp;
import com.hvisions.productBoard.resp.IndexOfficeDataOverviewResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndexOfficeService {

    @Resource
    private PmEnhanceService pmEnhanceService;
    
    @Resource
    private PmMoldingService pmMoldingService;
    
    @Resource
    private PmInspectionService pmInspectionService;
    
    @Resource
    private PmPackageService pmPackageService;

    public IndexOfficeDataOverviewResp getDataOverview() {
        return null;
    }

    public List<CompleteResp> getCompleteRateYear(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.YEAR);
    }

    public List<CompleteResp> getCompleteRateMonth(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.MONTH);
    }

    public List<CompleteResp> getCompleteRateWeek(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.WEEK);
    }

    @SuppressWarnings("unchecked")
    private List<CompleteResp> getCompleteRateByDepartmentAndPeriod(DepartmentEnum department, TimePeriodEnum period) {
        switch (department) {
            case ENHANCE:
                return (List<CompleteResp>) (List<?>) pmEnhanceService.getCompleteRate(period);
            case MOLDING:
                return (List<CompleteResp>) (List<?>) pmMoldingService.getCompleteRate(period);
            case INSPECTION:
                return (List<CompleteResp>) (List<?>) pmInspectionService.getOutput(period, InspectionTypeEnum.QUALITY);
            case PACKAGE:
                return (List<CompleteResp>) (List<?>) pmPackageService.getOutput(period, PackageIndexTypeEnum.PACKAGE);
            default:
                throw new IllegalArgumentException("不支持的部门类型: " + department);
        }
    }

    public String getUrgentOrder() {
        return null;
    }

    public String getUrgentOrderDetail() {
        return null;
    }
}
