package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.productBoard.entity.HyWmsCompleteDeliverOrder;
import com.hvisions.productBoard.entity.SaSaleOrder;
import com.hvisions.productBoard.enums.DepartmentEnum;
import com.hvisions.productBoard.enums.InspectionTypeEnum;
import com.hvisions.productBoard.enums.PackageIndexTypeEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.mapper.HyWmsCompleteDeliverOrderMapper;
import com.hvisions.productBoard.mapper.SaSaleOrderMapper;
import com.hvisions.productBoard.resp.CompleteResp;
import com.hvisions.productBoard.resp.IndexOfficeDataOverviewResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndexOfficeService {

    @Resource
    private PmEnhanceService pmEnhanceService;

    @Resource
    private PmMoldingService pmMoldingService;

    @Resource
    private PmInspectionService pmInspectionService;

    @Resource
    private PmPackageService pmPackageService;

    @Resource
    private SaSaleOrderMapper saSaleOrderMapper;

    @Resource
    private HyWmsCompleteDeliverOrderMapper hyWmsCompleteDeliverOrderMapper;

    public IndexOfficeDataOverviewResp getDataOverview() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(LocalTime.MAX);

        Integer newOrderCount = 0;
        try {
            Long count = saSaleOrderMapper.selectCount(
                    new QueryWrapper<SaSaleOrder>().lambda()
                            .between(SaSaleOrder::getCreateTime, todayStart, todayEnd)
            );
            newOrderCount = count != null ? count.intValue() : 0;
        } catch (Exception e) {
            log.error("查询新增合同数失败", e);
        }

        Integer productQuantity = 0;
        try {
            List<HyWmsCompleteDeliverOrder> deliverOrders = hyWmsCompleteDeliverOrderMapper.selectList(
                    new QueryWrapper<HyWmsCompleteDeliverOrder>().lambda()
                            .between(HyWmsCompleteDeliverOrder::getFinishDate, todayStart, todayEnd)
                            .isNotNull(HyWmsCompleteDeliverOrder::getActualQuantity)
            );

            BigDecimal totalQuantity = deliverOrders.stream()
                    .map(order -> order.getActualQuantity() != null ? order.getActualQuantity() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            productQuantity = totalQuantity.intValue();
        } catch (Exception e) {
            log.error("查询产品入库数失败", e);
        }

        Integer bottleneckOrderCount = 0;

        // 异常订单数 = 超期的备货需求单（预计完成日期 < 今天）
        Integer errorOrderCount = 0;
        try {
            Long count = saSaleOrderMapper.selectCount(
                    new QueryWrapper<SaSaleOrder>().lambda()
                            .lt(SaSaleOrder::getPlanFinishDate, today)
                            .notIn(SaSaleOrder::getShippingState, 20, 40) // 排除已完成发货的订单
            );
            errorOrderCount = count != null ? count.intValue() : 0;
        } catch (Exception e) {
            log.error("查询异常订单数失败", e);
        }

        // 临期订单数 = 交货时间一个月内的备货需求单（预计完成日期在今天到一个月内）
        Integer nearOrderCount = 0;
        try {
            LocalDate oneMonthLater = today.plusMonths(1);
            Long count = saSaleOrderMapper.selectCount(
                    new QueryWrapper<SaSaleOrder>().lambda()
                            .ge(SaSaleOrder::getPlanFinishDate, today)
                            .le(SaSaleOrder::getPlanFinishDate, oneMonthLater)
                            .notIn(SaSaleOrder::getShippingState, 20, 40) // 排除已完成发货的订单
            );
            nearOrderCount = count != null ? count.intValue() : 0;
        } catch (Exception e) {
            log.error("查询临期订单数失败", e);
        }

        return IndexOfficeDataOverviewResp.builder()
                .newOrderCount(newOrderCount)
                .productQuantity(productQuantity)
                .bottleneckOrderCount(bottleneckOrderCount)
                .errorOrderCount(errorOrderCount)
                .nearOrderCount(nearOrderCount)
                .build();
    }

    public List<CompleteResp> getCompleteRateYear(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.YEAR);
    }

    public List<CompleteResp> getCompleteRateMonth(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.MONTH);
    }

    public List<CompleteResp> getCompleteRateWeek(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.WEEK);
    }

    @SuppressWarnings("unchecked")
    private List<CompleteResp> getCompleteRateByDepartmentAndPeriod(DepartmentEnum department, TimePeriodEnum period) {
        switch (department) {
            case ENHANCE:
                return (List<CompleteResp>) (List<?>) pmEnhanceService.getCompleteRate(period);
            case MOLDING:
                return (List<CompleteResp>) (List<?>) pmMoldingService.getCompleteRate(period);
            case INSPECTION:
                return (List<CompleteResp>) (List<?>) pmInspectionService.getOutput(period, InspectionTypeEnum.QUALITY);
            case PACKAGE:
                return (List<CompleteResp>) (List<?>) pmPackageService.getOutput(period, PackageIndexTypeEnum.PACKAGE);
            default:
                throw new IllegalArgumentException("不支持的部门类型: " + department);
        }
    }

    public String getUrgentOrder() {
        return null;
    }

    public String getUrgentOrderDetail() {
        return null;
    }
}
