package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.productBoard.entity.HyQmVulcanizeQualityOrder;
import com.hvisions.productBoard.enums.VulcanizeOrderStateEnum;
import com.hvisions.productBoard.req.CommonPageReq;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface HyQmVulcanizeQualityOrderMapper extends BaseMapper<HyQmVulcanizeQualityOrder> {

    default List<HyQmVulcanizeQualityOrder> selectListForVulcanizeOrder(CommonPageReq req) {
        QueryWrapper<HyQmVulcanizeQualityOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(HyQmVulcanizeQualityOrder::getVulcanizeState,
                    VulcanizeOrderStateEnum.NEW.getValue(),
                    VulcanizeOrderStateEnum.RETURN_FACTORY.getValue())
                .orderByDesc(HyQmVulcanizeQualityOrder::getCreateTime);
        return this.selectList(queryWrapper);
    }
}