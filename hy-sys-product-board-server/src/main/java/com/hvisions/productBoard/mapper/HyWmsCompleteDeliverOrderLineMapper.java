package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.productBoard.entity.HyWmsCompleteDeliverOrderLine;
import com.hvisions.productBoard.req.BiWarehouseOrderReq;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Mapper
public interface HyWmsCompleteDeliverOrderLineMapper extends BaseMapper<HyWmsCompleteDeliverOrderLine> {

    default List<HyWmsCompleteDeliverOrderLine> selectListForWarehouseOrder(BiWarehouseOrderReq req) {
        if (CollectionUtils.isEmpty(req.getCodes())) {
            return new ArrayList<>();
        }

        QueryWrapper<HyWmsCompleteDeliverOrderLine> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(HyWmsCompleteDeliverOrderLine::getCode, req.getCodes())
                .orderByDesc(HyWmsCompleteDeliverOrderLine::getCreateTime);
        return this.selectList(queryWrapper);
    }
}