package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.productBoard.entity.HyPmPkgOrderLine;
import com.hvisions.productBoard.req.BiPackageOrderReq;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Mapper
public interface HyPmPkgOrderLineMapper extends BaseMapper<HyPmPkgOrderLine> {

    default List<HyPmPkgOrderLine> selectListForPackageOrder(BiPackageOrderReq req) {
        if (CollectionUtils.isEmpty(req.getCodes())) {
            return new ArrayList<>();
        }

        QueryWrapper<HyPmPkgOrderLine> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(HyPmPkgOrderLine::getCode, req.getCodes())
                .orderByDesc(HyPmPkgOrderLine::getCreateTime);
        return this.selectList(queryWrapper);
    }
}