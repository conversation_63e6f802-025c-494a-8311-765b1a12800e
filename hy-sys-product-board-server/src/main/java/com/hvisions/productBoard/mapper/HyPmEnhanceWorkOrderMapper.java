package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.productBoard.entity.HyPmEnhanceWorkOrder;
import com.hvisions.productBoard.enums.EnhanceWorkOrderStateEnum;
import com.hvisions.productBoard.req.CommonPageReq;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface HyPmEnhanceWorkOrderMapper extends BaseMapper<HyPmEnhanceWorkOrder> {

    default List<HyPmEnhanceWorkOrder> selectListForKeyOrder(CommonPageReq req) {
        QueryWrapper<HyPmEnhanceWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(HyPmEnhanceWorkOrder::getState, EnhanceWorkOrderStateEnum.PRODUCING.getValue())
                .orderByAsc(HyPmEnhanceWorkOrder::getPlanEndTime);
        return this.selectList(queryWrapper);
    }
}