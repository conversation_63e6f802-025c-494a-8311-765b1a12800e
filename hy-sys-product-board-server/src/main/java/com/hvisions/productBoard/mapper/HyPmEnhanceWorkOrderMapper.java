package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.productBoard.entity.HyPmEnhanceWorkOrder;
import com.hvisions.productBoard.enums.EnhanceWorkOrderStateEnum;
import com.hvisions.productBoard.req.CommonPageReq;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

import java.util.List;

@Mapper
public interface HyPmEnhanceWorkOrderMapper extends BaseMapper<HyPmEnhanceWorkOrder> {

    default List<HyPmEnhanceWorkOrder> selectListForKeyOrder(CommonPageReq req, String orderCode, String productName, String productEigenvalue, String batchNum) {
        QueryWrapper<HyPmEnhanceWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(HyPmEnhanceWorkOrder::getState, EnhanceWorkOrderStateEnum.PRODUCING.getValue())
                .like(StringUtils.hasText(orderCode), HyPmEnhanceWorkOrder::getCode, orderCode)
                .like(StringUtils.hasText(productName), HyPmEnhanceWorkOrder::getMaterialName, productName)
                .like(StringUtils.hasText(productEigenvalue), HyPmEnhanceWorkOrder::getMaterialEigenvalue, productEigenvalue)
                .like(StringUtils.hasText(batchNum), HyPmEnhanceWorkOrder::getMaterialBatchNum, batchNum)
                .orderByAsc(HyPmEnhanceWorkOrder::getPlanEndTime);
        return this.selectList(queryWrapper);
    }
}