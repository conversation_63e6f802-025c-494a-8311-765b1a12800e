package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.enums.DepartmentEnum;
import com.hvisions.productBoard.resp.CompleteResp;
import com.hvisions.productBoard.resp.IndexOfficeDataOverviewResp;
import com.hvisions.productBoard.resp.IndexOfficeUrgentOrderResp;
import com.hvisions.productBoard.service.IndexOfficeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "生产首页 - 办公室")
@RestController
@RequiredArgsConstructor
@RequestMapping("/index/office")
public class IndexOfficeController {

    @Resource
    private IndexOfficeService indexOfficeService;

    @ApiOperation(value = "数据概览")
    @GetMapping("/dataOverview")
    public IndexOfficeDataOverviewResp getDataOverview() {
        return indexOfficeService.getDataOverview();
    }

    @ApiOperation(value = "计划达成率（年）")
    @GetMapping("/rateYear")
    public List<CompleteResp> getRateYear(@ApiParam(value = "部门", required = true) @RequestParam DepartmentEnum department) {
        return indexOfficeService.getCompleteRateYear(department);
    }

    @ApiOperation(value = "计划达成率（月）")
    @GetMapping("/rateMonth")
    public List<CompleteResp> getRateMonth(@ApiParam(value = "部门", required = true) @RequestParam DepartmentEnum department) {
        return indexOfficeService.getCompleteRateMonth(department);
    }

    @ApiOperation(value = "计划达成率（周）")
    @GetMapping("/rateWeek")
    public List<CompleteResp> getRateWeek(@ApiParam(value = "部门", required = true) @RequestParam DepartmentEnum department) {
        return indexOfficeService.getCompleteRateWeek(department);
    }

    @ApiOperation(value = "临期订单")
    @PostMapping("/urgentOrder")
    public Page<IndexOfficeUrgentOrderResp> getUrgentOrder(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam(value = "订单类型", required = true) @RequestParam String type,
            @ApiParam(value = "合同号") @RequestParam(required = false) String contractCode) {
        return indexOfficeService.getUrgentOrder(current, size, type, contractCode);
    }

    @ApiOperation(value = "临期订单明细")
    @PostMapping("/urgentOrderDetail")
    public String getUrgentOrderDetail() {
        return indexOfficeService.getUrgentOrderDetail();
    }
}
