package com.hvisions.productBoard.req;

import com.hvisions.common.dto.PageInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class BiWarehouseOrderReq extends PageInfo {

    private List<String> codes;

    public static BiWarehouseOrderReq of(Integer current, Integer size, List<String> codes) {
        BiWarehouseOrderReq req = new BiWarehouseOrderReq();
        req.setPage(current);
        req.setPageSize(size);
        req.setCodes(codes);
        return req;
    }
}
