package com.hvisions.productBoard.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel(value = "临期订单查询请求")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexOfficeUrgentOrderReq {
    
    @ApiModelProperty(value = "当前页", example = "1")
    private Integer current = 1;
    
    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer size = 10;
    
    @ApiModelProperty(value = "订单类型", required = true, example = "销售")
    private String type;
    
    @ApiModelProperty(value = "合同号", example = "CONTRACT001")
    private String contractCode;
}
