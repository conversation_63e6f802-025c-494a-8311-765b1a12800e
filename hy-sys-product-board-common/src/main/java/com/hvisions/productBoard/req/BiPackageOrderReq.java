package com.hvisions.productBoard.req;

import com.hvisions.common.dto.PageInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class BiPackageOrderReq extends PageInfo {

    private List<String> codes;

    public static BiPackageOrderReq of(Integer current, Integer size, List<String> codes) {
        BiPackageOrderReq req = new BiPackageOrderReq();
        req.setPage(current);
        req.setPageSize(size);
        req.setCodes(codes);
        return req;
    }
}
