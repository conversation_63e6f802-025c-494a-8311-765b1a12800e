package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class IndexOfficeUrgentOrderResp {
    @ApiModelProperty(value = "合同号")
    private String contractCode;
    @ApiModelProperty(value = "客户单位")
    private String customerName;
    @ApiModelProperty(value = "交付日期")
    private String deliveryDate;
    @ApiModelProperty(value = "进度")
    private String progress;
}
