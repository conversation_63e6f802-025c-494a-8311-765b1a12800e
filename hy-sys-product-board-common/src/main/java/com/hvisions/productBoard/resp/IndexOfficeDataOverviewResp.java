package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexOfficeDataOverviewResp {
    @ApiModelProperty(value = "新增合同数")
    private Integer newOrderCount;
    @ApiModelProperty(value = "产品入库数")
    private Integer productQuantity;
    @ApiModelProperty(value = "瓶颈订单数")
    private Integer bottleneckOrderCount;
    @ApiModelProperty(value = "异常订单数")
    private Integer errorOrderCount;
    @ApiModelProperty(value = "临期订单数")
    private Integer nearOrderCount;
}
