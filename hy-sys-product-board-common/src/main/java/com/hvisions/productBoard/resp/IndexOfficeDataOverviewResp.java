package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class IndexOfficeDataOverviewResp {
    @ApiModelProperty(value = "新增合同数")
    private Long newOrderCount;
    @ApiModelProperty(value = "产品入库数")
    private Long productQuantity;
    @ApiModelProperty(value = "瓶颈订单数")
    private Long bottleneckOrderCount;
    @ApiModelProperty(value = "异常订单数")
    private Long errorOrderCount;
    @ApiModelProperty(value = "临期订单数")
    private Long nearOrderCount;
}
